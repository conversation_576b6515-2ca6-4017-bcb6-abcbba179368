import React, { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { fetchCustomers } from '../store/slices/customersSlice'
import type { RootState, AppDispatch } from '../store'
import { List, Spin, Alert } from 'antd'
import { Customer } from '../interfaces'

const Customers: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { items, loading, error } = useSelector((state: RootState) => state.customers) as {
    items: Customer[]
    loading: boolean
    error: string | null
  }

  useEffect(() => {
    dispatch(fetchCustomers())
  }, [dispatch])
  if (loading) return (
    <Spin tip="Loading customers...">
      <div style={{ textAlign: 'center', padding: '50px', minHeight: '200px' }} />
    </Spin>
  )
  if (error) return <Alert message="Error" description={error} type="error" showIcon />

  return (<List
    header={<div>Customers</div>}
    bordered
    dataSource={Array.isArray(items) ? items : []}
    renderItem={(item: Customer) => (
      <List.Item>
        <List.Item.Meta
          title={item.name}
          description={item.email}
        />
      </List.Item>
    )}
  />
  )
}

export default Customers
