import React, { useState } from 'react'
import { Button, Modal, Form, Input, Typography, Space } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { 
  Loading<PERSON><PERSON><PERSON>, 
  <PERSON>rror<PERSON><PERSON>t, 
  DataTable,
  showDeleteConfirm, 
  showUpdateConfirm 
} from '../components'
import { useApi, useMutation } from '../hooks'
import { categoriesService } from '../client/services'
import type { Category, CategoryCreate, CategoryUpdate, TableColumn } from '../client/types'
import { formatDate, formatId, commonRules } from '../utils'

const { Title, Text } = Typography

const Categories: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add')
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [form] = Form.useForm()

  // Fetch categories
  const { 
    data: categoriesResponse, 
    loading, 
    error, 
    execute: refetchCategories 
  } = useApi(() => categoriesService.getAll())

  // Mutations
  const createMutation = useMutation(
    (data: CategoryCreate) => categoriesService.create(data),
    { onSuccess: () => { refetchCategories(); setIsModalVisible(false); form.resetFields() } }
  )

  const updateMutation = useMutation(
    ({ id, data }: { id: string; data: CategoryUpdate }) => categoriesService.update(id, data),
    { onSuccess: () => { refetchCategories(); setIsModalVisible(false); form.resetFields() } }
  )

  const deleteMutation = useMutation(
    (id: string) => categoriesService.delete(id),
    { onSuccess: () => refetchCategories() }
  )

  const categories = categoriesResponse?.data || []

  // Handlers
  const showAddModal = () => {
    setModalMode('add')
    setEditingCategory(null)
    form.resetFields()
    setIsModalVisible(true)
  }

  const showEditModal = (category: Category) => {
    setModalMode('edit')
    setEditingCategory(category)
    form.setFieldsValue({
      name: category.name,
      description: category.description,
    })
    setIsModalVisible(true)
  }

  const handleDelete = (category: Category) => {
    showDeleteConfirm(category.name, () => deleteMutation.mutate(category.id))
  }

  const handleCancel = () => {
    form.resetFields()
    setIsModalVisible(false)
  }

  const onFinish = async (values: CategoryCreate | CategoryUpdate) => {
    if (modalMode === 'add') {
      createMutation.mutate(values as CategoryCreate)
    } else if (modalMode === 'edit' && editingCategory) {
      showUpdateConfirm(values.name || editingCategory.name, () => {
        updateMutation.mutate({ id: editingCategory.id, data: values })
      })
    }
  }

  // Table columns
  const columns: TableColumn<Category>[] = [
    {
      key: 'name',
      title: 'Name',
      dataIndex: 'name',
      sorter: true,
    },
    {
      key: 'description',
      title: 'Description',
      dataIndex: 'description',
      render: (description: string) => description || 'No description',
    },
    {
      key: 'id',
      title: 'ID',
      dataIndex: 'id',
      render: (id: string) => formatId(id),
      width: 100,
    },
    {
      key: 'created_at',
      title: 'Created',
      dataIndex: 'created_at',
      render: (date: string) => formatDate(date),
      width: 120,
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record: Category) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
            size="small"
          >
            Edit
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record)}
            size="small"
          >
            Delete
          </Button>
        </Space>
      ),
      width: 120,
    },
  ]

  if (loading) return <LoadingSpinner tip="Loading categories..." />
  if (error) return <ErrorAlert description={error} onRetry={refetchCategories} />

  return (
    <>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Categories</Title>
        <Text type="secondary">Manage product categories</Text>
      </div>

      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={showAddModal}
          loading={createMutation.loading}
        >
          Add Category
        </Button>
      </div>

      <DataTable
        data={categories}
        columns={columns}
        loading={loading || deleteMutation.loading}
        emptyText="No categories found. Click 'Add Category' to create your first category."
      />

      <Modal
        title={modalMode === 'add' ? 'Add Category' : 'Edit Category'}
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={() => form.submit()}
        okText={modalMode === 'add' ? 'Add' : 'Update'}
        confirmLoading={createMutation.loading || updateMutation.loading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ name: '', description: '' }}
        >
          <Form.Item
            name="name"
            label="Name"
            rules={commonRules.name}
          >
            <Input placeholder="Enter category name" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
            rules={commonRules.description}
          >
            <Input.TextArea 
              rows={3} 
              placeholder="Enter category description (optional)"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default Categories
