import React, { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { fetchOrders } from '../store/slices/ordersSlice'
import type { RootState, AppDispatch } from '../store'
import { List, Spin, Alert } from 'antd'
import { Order } from '../interfaces'

const Orders: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { items, loading, error } = useSelector((state: RootState) => state.orders) as {
    items: Order[]
    loading: boolean
    error: string | null
  }

  useEffect(() => {
    dispatch(fetchOrders())
  }, [dispatch])
  if (loading) return (
    <Spin tip="Loading orders...">
      <div style={{ textAlign: 'center', padding: '50px', minHeight: '200px' }} />
    </Spin>
  )
  if (error) return <Alert message="Error" description={error} type="error" showIcon />

  return (<List
    header={<div>Orders</div>}
    bordered
    dataSource={Array.isArray(items) ? items : []}
    renderItem={(item: Order) => (
      <List.Item>
        <List.Item.Meta
          title={`Order #${item.id}`}
          description={`Status: ${item.status} - Total: $${item.total_amount}`}
        />
      </List.Item>
    )}
  />
  )
}

export default Orders
