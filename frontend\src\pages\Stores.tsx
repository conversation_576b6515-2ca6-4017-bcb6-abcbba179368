import React, { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { fetchStores } from '../store/slices/storesSlice'
import type { RootState, AppDispatch } from '../store'
import { List, Spin, Alert } from 'antd'
import { Store } from '../interfaces'

const Stores: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { items, loading, error } = useSelector((state: RootState) => state.stores) as {
    items: Store[]
    loading: boolean
    error: string | null
  }

  useEffect(() => {
    dispatch(fetchStores())
  }, [dispatch])
  if (loading) return (
    <Spin tip="Loading stores...">
      <div style={{ textAlign: 'center', padding: '50px', minHeight: '200px' }} />
    </Spin>
  )
  if (error) return <Alert message="Error" description={error} type="error" showIcon />

  return (<List
    header={<div>Stores</div>}
    bordered
    dataSource={Array.isArray(items) ? items : []}
    renderItem={(item: Store) => (
      <List.Item>
        <List.Item.Meta
          title={item.name}
          description={item.address}
        />
      </List.Item>
    )}
  />
  )
}

export default Stores
