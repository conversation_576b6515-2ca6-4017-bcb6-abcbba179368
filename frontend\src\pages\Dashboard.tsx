import React, { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { fetchProducts } from '../store/slices/productsSlice'
import { fetchCategories } from '../store/slices/categoriesSlice'
import { fetchOrders } from '../store/slices/ordersSlice'
import { fetchCustomers } from '../store/slices/customersSlice'
import { fetchStores } from '../store/slices/storesSlice'
import type { RootState, AppDispatch } from '../store'
import { Card, Row, Col, Spin, Alert } from 'antd'

const Dashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()

  const products = useSelector((state: RootState) => state.products) as {
    items: any[]
    loading: boolean
    error: string | null
  }
  const categories = useSelector((state: RootState) => state.categories) as {
    items: any[]
    loading: boolean
    error: string | null
  }
  const orders = useSelector((state: RootState) => state.orders) as {
    items: any[]
    loading: boolean
    error: string | null
  }
  const customers = useSelector((state: RootState) => state.customers) as {
    items: any[]
    loading: boolean
    error: string | null
  }
  const stores = useSelector((state: RootState) => state.stores) as {
    items: any[]
    loading: boolean
    error: string | null
  }

  useEffect(() => {
    dispatch(fetchProducts({}))
    dispatch(fetchCategories())
    dispatch(fetchOrders())
    dispatch(fetchCustomers())
    dispatch(fetchStores())
  }, [dispatch])

  if (
    products.loading ||
    categories.loading ||
    orders.loading ||
    customers.loading ||
    stores.loading) return (
      <Spin tip="Loading dashboard...">
        <div style={{ textAlign: 'center', padding: '50px', minHeight: '200px' }} />
      </Spin>
    )

  if (
    products.error ||
    categories.error ||
    orders.error ||
    customers.error ||
    stores.error
  )
    return (
      <Alert
        message="Error"
        description="Failed to load dashboard data"
        type="error"
        showIcon
      />
    )

  return (
    <Row gutter={16}>
      <Col span={4}>        <Card title="Products" variant="borderless">
        {products.items.length}
      </Card>
      </Col>
      <Col span={4}>
        <Card title="Categories" variant="borderless">
          {categories.items.length}
        </Card>
      </Col>
      <Col span={4}>
        <Card title="Orders" variant="borderless">
          {orders.items.length}
        </Card>
      </Col>
      <Col span={4}>
        <Card title="Customers" variant="borderless">
          {customers.items.length}
        </Card>
      </Col>
      <Col span={4}>
        <Card title="Stores" variant="borderless">
          {stores.items.length}
        </Card>
      </Col>
    </Row>
  )
}

export default Dashboard
