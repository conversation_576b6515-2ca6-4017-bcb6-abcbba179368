import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../utils/api'
import { OrderDetail } from '../../interfaces'

interface OrderDetailsState {
  items: OrderDetail[];
  loading: boolean;
  error: string | null;
}

const initialState: OrderDetailsState = {
  items: [],
  loading: false,
  error: null
}

export const fetchOrderDetails = createAsyncThunk(
  'orderDetails/fetchOrderDetails',
  async () => {
    const response = await api.get('/order_details')
    return response.data
  }
)

export const createOrderDetail = createAsyncThunk(
  'orderDetails/createOrderDetail',
  async (orderDetail: Partial<OrderDetail>) => {
    const response = await api.post('/order_details', orderDetail)
    return response.data
  }
)

export const updateOrderDetail = createAsyncThunk(
  'orderDetails/updateOrderDetail',
  async ({ id, ...orderDetail }: Partial<OrderDetail> & { id: string }) => {
    const response = await api.put(`/order_details/${id}`, orderDetail)
    return response.data
  }
)

export const deleteOrderDetail = createAsyncThunk(
  'orderDetails/deleteOrderDetail',
  async (id: string) => {
    await api.delete(`/order_details/${id}`)
    return id
  }
)

const orderDetailsSlice = createSlice({
  name: 'orderDetails',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchOrderDetails.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchOrderDetails.fulfilled, (state, action) => {
        state.loading = false
        state.items = action.payload.data
      })
      .addCase(fetchOrderDetails.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch order details'
      })
      .addCase(createOrderDetail.fulfilled, (state, action) => {
        state.items.push(action.payload)
      })
      .addCase(updateOrderDetail.fulfilled, (state, action) => {
        const index = state.items.findIndex(item => item.id === action.payload.id)
        if (index !== -1) {
          state.items[index] = action.payload
        }
      })
      .addCase(deleteOrderDetail.fulfilled, (state, action) => {
        state.items = state.items.filter(item => item.id !== action.payload)
      })
  }
})

export const { reducer } = orderDetailsSlice
