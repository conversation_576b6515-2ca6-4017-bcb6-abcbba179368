import { apiClient } from './api'
import type {
  Product,
  ProductCreate,
  ProductUpdate,
  ProductsParams,
  Category,
  CategoryCreate,
  CategoryUpdate,
  Store,
  StoreCreate,
  StoreUpdate,
  Customer,
  CustomerCreate,
  CustomerUpdate,
  Order,
  OrderCreate,
  OrderUpdate,
  Variant,
  VariantCreate,
  VariantUpdate,
  VariantsParams,
  OrderDetail,
  OrderDetailCreate,
  OrderDetailUpdate,
  OrderDetailsParams,
  ApiResponse,
  PaginatedResponse,
} from './types'

// Products Service
export const productsService = {
  getAll: (params?: ProductsParams) =>
    apiClient.get<PaginatedResponse<Product>>('/products', params),
  
  getById: (id: string) =>
    apiClient.get<Product>(`/products/${id}`),
  
  create: (data: ProductCreate) =>
    apiClient.post<Product>('/products', data),
  
  update: (id: string, data: ProductUpdate) =>
    apiClient.put<Product>(`/products/${id}`, data),
  
  delete: (id: string) =>
    apiClient.delete(`/products/${id}`),
}

// Categories Service
export const categoriesService = {
  getAll: () =>
    apiClient.get<ApiResponse<Category[]>>('/categories'),
  
  getById: (id: string) =>
    apiClient.get<Category>(`/categories/${id}`),
  
  create: (data: CategoryCreate) =>
    apiClient.post<Category>('/categories', data),
  
  update: (id: string, data: CategoryUpdate) =>
    apiClient.put<Category>(`/categories/${id}`, data),
  
  delete: (id: string) =>
    apiClient.delete(`/categories/${id}`),
}

// Stores Service
export const storesService = {
  getAll: () =>
    apiClient.get<ApiResponse<Store[]>>('/stores'),
  
  getById: (id: string) =>
    apiClient.get<Store>(`/stores/${id}`),
  
  create: (data: StoreCreate) =>
    apiClient.post<Store>('/stores', data),
  
  update: (id: string, data: StoreUpdate) =>
    apiClient.put<Store>(`/stores/${id}`, data),
  
  delete: (id: string) =>
    apiClient.delete(`/stores/${id}`),
}

// Customers Service
export const customersService = {
  getAll: () =>
    apiClient.get<ApiResponse<Customer[]>>('/customers'),
  
  getById: (id: string) =>
    apiClient.get<Customer>(`/customers/${id}`),
  
  create: (data: CustomerCreate) =>
    apiClient.post<Customer>('/customers', data),
  
  update: (id: string, data: CustomerUpdate) =>
    apiClient.put<Customer>(`/customers/${id}`, data),
  
  delete: (id: string) =>
    apiClient.delete(`/customers/${id}`),
}

// Orders Service
export const ordersService = {
  getAll: () =>
    apiClient.get<ApiResponse<Order[]>>('/orders'),
  
  getById: (id: string) =>
    apiClient.get<Order>(`/orders/${id}`),
  
  create: (data: OrderCreate) =>
    apiClient.post<Order>('/orders', data),
  
  update: (id: string, data: OrderUpdate) =>
    apiClient.put<Order>(`/orders/${id}`, data),
  
  delete: (id: string) =>
    apiClient.delete(`/orders/${id}`),
}

// Variants Service
export const variantsService = {
  getAll: (params?: VariantsParams) =>
    apiClient.get<ApiResponse<Variant[]>>('/variants', params),
  
  getById: (id: string) =>
    apiClient.get<Variant>(`/variants/${id}`),
  
  getByProduct: (productId: string) =>
    apiClient.get<ApiResponse<Variant[]>>('/variants', { product_id: productId }),
  
  create: (data: VariantCreate) =>
    apiClient.post<Variant>('/variants', data),
  
  update: (id: string, data: VariantUpdate) =>
    apiClient.put<Variant>(`/variants/${id}`, data),
  
  delete: (id: string) =>
    apiClient.delete(`/variants/${id}`),
}

// Order Details Service
export const orderDetailsService = {
  getAll: (params?: OrderDetailsParams) =>
    apiClient.get<ApiResponse<OrderDetail[]>>('/order_details', params),
  
  getById: (id: string) =>
    apiClient.get<OrderDetail>(`/order_details/${id}`),
  
  getByOrder: (orderId: string) =>
    apiClient.get<ApiResponse<OrderDetail[]>>('/order_details', { order_id: orderId }),
  
  create: (data: OrderDetailCreate) =>
    apiClient.post<OrderDetail>('/order_details', data),
  
  update: (id: string, data: OrderDetailUpdate) =>
    apiClient.put<OrderDetail>(`/order_details/${id}`, data),
  
  delete: (id: string) =>
    apiClient.delete(`/order_details/${id}`),
}

// Export all services
export const services = {
  products: productsService,
  categories: categoriesService,
  stores: storesService,
  customers: customersService,
  orders: ordersService,
  variants: variantsService,
  orderDetails: orderDetailsService,
}

export default services
