{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx}\""}, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.2", "axios": "^1.6.2", "dayjs": "^1.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.21.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "typescript": "^5.3.3", "vite": "^5.0.8", "@vitejs/plugin-react-swc": "^3.5.0"}}