import React, { useEffect, useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { fetchProducts, createProduct, updateProduct, deleteProduct } from '../store/slices/productsSlice'
import type { RootState, AppDispatch } from '../store'
import { Row, Col, Card, Button, Modal, Form, Input, Spin, Alert, Pagination } from 'antd'
import { Product } from '../interfaces'

const Products: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { items, total, loading, error } = useSelector((state: RootState) => state.products)
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 9

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add')
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)

  const [form] = Form.useForm()

  useEffect(() => {
    dispatch(fetchProducts({ page: currentPage, pageSize }))
  }, [dispatch, currentPage])

  const showAddModal = () => {
    setModalMode('add')
    setEditingProduct(null)
    form.resetFields()
    setIsModalVisible(true)
  }

  const showEditModal = (product: Product) => {
    setModalMode('edit')
    setEditingProduct(product)
    form.setFieldsValue({
      name: product.name,
      descriptions: product.descriptions,
      link_image: product.link_image,
    })
    setIsModalVisible(true)
  }

  const handleDelete = (id: string) => {
    dispatch(deleteProduct(id))
  }

  const handleCancel = () => {
    setIsModalVisible(false)
  }

  const onFinish = (values: any) => {
    if (modalMode === 'add') {
      dispatch(createProduct(values))
    } else if (modalMode === 'edit' && editingProduct) {
      dispatch(updateProduct({ id: editingProduct.id, ...values }))
    }
    setIsModalVisible(false)
  }

  if (loading) return (
    <div style={{ textAlign: 'center', padding: '50px' }}>
      <Spin tip="Loading products...">
        <div style={{ minHeight: '200px' }} />
      </Spin>
    </div>
  )

  if (error) return <Alert message="Error" description={error} type="error" showIcon />

  return (
    <>
      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        <Button type="primary" onClick={showAddModal}>Add Product</Button>
      </div>
      <Row gutter={[16, 16]}>
        {items.map((item: Product) => (
          <Col key={item.id} span={8}>
            <Card
              size="small"
              cover={
                item.link_image ? (
                  <div style={{
                    height: '200px',
                    width: '100%',
                    overflow: 'hidden',
                    backgroundColor: '#f5f5f5',
                    position: 'relative'
                  }}>
                    <img
                      alt={item.name}
                      src={item.link_image}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        objectPosition: 'center',
                        display: 'block'
                      }}
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.parentElement!.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999; font-size: 14px;">No Image</div>';
                      }}
                    />
                  </div>
                ) : (
                  <div style={{
                    height: '200px',
                    backgroundColor: '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#999',
                    fontSize: '14px'
                  }}>
                    No Image
                  </div>
                )
              }
              actions={[
                <Button type="link" size="small" onClick={() => showEditModal(item)}>Edit</Button>,
                <Button type="link" size="small" danger onClick={() => handleDelete(item.id)}>Delete</Button>,
              ]}
              style={{ height: '100%' }}
            >
              <Card.Meta
                title={<div style={{ fontSize: '14px', fontWeight: 'bold' }}>{item.name}</div>}
                description={
                  <div style={{
                    fontSize: '12px',
                    color: '#666',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}>
                    {item.descriptions || 'No description'}
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={total}
        onChange={page => setCurrentPage(page)}
        style={{ marginTop: 16, textAlign: 'center' }}
      />
      <Modal
        title={modalMode === 'add' ? 'Add Product' : 'Edit Product'}
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={() => form.submit()}
        okText={modalMode === 'add' ? 'Add' : 'Update'}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ name: '', descriptions: '', link_image: '' }}
        >
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please input the product name!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="descriptions"
            label="Description"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="link_image"
            label="Image URL"
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default Products
